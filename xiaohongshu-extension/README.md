# 🚀 小红书智能助手 Chrome Extension

> 世界顶级程序员打造的专业级小红书内容管理工具

一款功能强大的Chrome扩展，专为内容创作者和运营人员设计，提供智能化的小红书内容采集、AI改写和数据管理解决方案。

## ✨ 核心特性

### 📝 智能内容采集
- **一键采集**：自动提取标题、内容、图片、标签等完整信息
- **精准识别**：智能检测小红书笔记详情页，确保采集准确性
- **批量处理**：支持多页面连续采集，提升工作效率
- **实时预览**：采集前可预览内容，确保质量

### 🤖 AI智能改写
- **DeepSeek集成**：基于最新AI模型，提供高质量改写服务
- **多样风格**：支持轻松、专业、简洁三种改写风格
- **智能优化**：自动调整语言表达，保持原意的同时提升可读性
- **相似度检测**：实时显示改写内容与原文的相似度

### 📊 飞书数据管理
- **无缝集成**：直接保存到飞书多维表格，便于团队协作
- **结构化存储**：自动分类存储标题、内容、图片等信息
- **批量操作**：支持批量导入导出，提升数据处理效率
- **实时同步**：数据实时同步到云端，确保安全可靠

### 🎨 极致用户体验
- **直观界面**：现代化UI设计，操作简单直观
- **智能引导**：首次使用自动引导配置，快速上手
- **状态反馈**：实时显示连接状态和操作进度
- **响应式布局**：完美适配各种屏幕尺寸

## 🛠 安装配置

### 快速安装

1. **下载扩展**
   ```bash
   git clone <repository-url>
   cd xiaohongshu-extension
   npm install
   npm run build
   ```

2. **加载到Chrome**
   - 打开 `chrome://extensions/`
   - 开启"开发者模式"
   - 点击"加载已解压的扩展程序"
   - 选择项目 `dist` 文件夹

### 服务配置

#### 🐣 飞书配置（数据存储）
1. 访问 [飞书开放平台](https://open.feishu.cn/app) 创建应用
2. 获取 App ID 和 App Secret
3. 配置权限：
   - ✅ 查看、编辑多维表格
   - ✅ 查看、创建、更新、删除多维表格记录
4. 在扩展设置中填入配置信息

#### 🧠 DeepSeek配置（AI服务）
1. 访问 [DeepSeek平台](https://platform.deepseek.com/sign_up) 注册账号
2. 获取 API Key
3. 在扩展设置中配置密钥

**💰 费用说明**: DeepSeek提供免费额度，新用户送$5，每月送$5，改写一篇文章约$0.01-0.05

## 📖 使用指南

### 基础操作

1. **采集笔记**
   - 浏览小红书笔记详情页
   - 点击扩展图标或使用 `Ctrl+Shift+S`
   - 在弹出对话框中确认信息
   - 选择是否进行AI改写

2. **批量采集**
   - 点击"批量模式"按钮
   - 依次访问多个笔记页面
   - 系统自动采集并统计数量
   - 完成后点击"全部保存"

3. **内容管理**
   - 在设置页面查看所有数据
   - 支持导出为JSON格式
   - 可直接打开飞书表格管理

### 高级功能

- **智能页面检测**：自动识别有效的笔记页面
- **错误处理**：完善的异常处理和用户提示
- **数据备份**：支持本地数据导入导出
- **状态监控**：实时显示服务连接状态

## 🏗 技术架构

### 前端技术栈
- **TypeScript**: 类型安全的JavaScript超集
- **Chrome Extension API**: Manifest V3架构
- **Modern CSS**: 响应式设计 + 动画效果
- **Webpack**: 模块打包和构建优化

### 核心模块
```
src/
├── background.ts          # Service Worker 后台服务
├── content-script.ts      # 页面内容脚本
├── popup/                 # 扩展弹窗界面
├── options/               # 设置配置页面
└── utils/                 # 核心工具模块
    ├── extractor.ts       # 智能内容提取
    ├── deepseek-api.ts    # AI服务集成
    ├── feishu-api.ts      # 飞书API集成
    └── storage.ts         # 加密存储管理
```

### 数据流设计
```
小红书页面 → 内容提取 → AI改写 → 飞书存储 → 数据管理
```

## 🔧 开发指南

### 环境要求
- Node.js 16+
- Chrome 90+
- npm/yarn

### 开发命令
```bash
# 开发模式（热重载）
npm run dev

# 生产构建
npm run build

# 类型检查
npm run check-types

# 代码格式化
npm run format
```

### 代码规范
- **TypeScript严格模式**：确保类型安全
- **ES6+语法**：使用现代JavaScript特性
- **模块化设计**：单一职责，高内聚低耦合
- **错误处理**：完善的异常捕获和用户反馈

## 🛡 安全特性

- **数据加密**：本地敏感数据加密存储
- **权限最小化**：仅申请必要的扩展权限
- **安全通信**：HTTPS协议确保数据传输安全
- **隐私保护**：不收集用户个人信息

## 📈 性能优化

- **代码分割**：按需加载，减少初始包大小
- **缓存策略**：智能缓存提升响应速度
- **异步处理**：非阻塞操作确保流畅体验
- **内存管理**：及时清理资源，避免内存泄漏

## 🐛 故障排除

### 常见问题

**Q: 扩展无法采集内容？**
A: 请确保在小红书笔记详情页，而非首页或搜索页

**Q: API连接失败？**
A: 检查网络连接和API配置是否正确

**Q: 飞书表格无法访问？**
A: 确认应用权限配置和App Secret是否正确

### 调试技巧
1. 开启开发者模式查看控制台日志
2. 检查扩展权限是否正确授予
3. 验证网络连接和API配置

## 🤝 贡献指南

我们欢迎各种形式的贡献：

1. **Bug报告**：发现问题请创建Issue
2. **功能建议**：提出改进建议
3. **代码贡献**：提交Pull Request
4. **文档完善**：改进使用文档

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🎯 发展路线

### v1.1.0 计划
- [ ] 支持更多AI模型（GPT-4、Claude等）
- [ ] 增加图片OCR文字识别
- [ ] 支持视频内容分析
- [ ] 添加内容去重功能

### v1.2.0 计划
- [ ] 支持其他平台（抖音、快手等）
- [ ] 团队协作功能
- [ ] 数据分析看板
- [ ] API开放接口

---

<div align="center">

**🌟 如果这个项目对你有帮助，请给个Star支持一下！**

Made with ❤️ by World-Class Developers

</div>